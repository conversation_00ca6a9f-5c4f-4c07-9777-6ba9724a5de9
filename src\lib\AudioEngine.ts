import * as Tone from 'tone';

interface GuitarString {
  name: string;
  baseNote: string;
}

export class AudioEngine {
  private static synth: Tone.PolySynth | null = null;
  private static reverb: Tone.Reverb | null = null;
  private static delay: Tone.FeedbackDelay | null = null;
  private static chorus: Tone.Chorus | null = null;

  private static strings: GuitarString[] = [
    { name: 'e', baseNote: 'E4' },
    { name: 'B', baseNote: 'B3' },
    { name: 'G', baseNote: 'G3' },
    { name: 'D', baseNote: 'D3' },
    { name: 'A', baseNote: 'A2' },
    { name: 'E', baseNote: 'E2' }
  ];

  static async initialize() {
    await Tone.start();

    this.synth = new Tone.PolySynth(Tone.Synth, {
      oscillator: {
        type: 'triangle'
      },
      envelope: {
        attack: 0.005,
        decay: 0.1,
        sustain: 0.3,
        release: 1
      }
    }).toDestination();

    // Effects chain
    this.reverb = new Tone.Reverb(1.5).toDestination();
    this.delay = new Tone.FeedbackDelay('8n', 0.5).toDestination();
    this.chorus = new Tone.Chorus(4, 2.5, 0.5).toDestination();

    this.synth.connect(this.reverb);
    this.synth.connect(this.delay);
    this.synth.connect(this.chorus);
  }

  static playNote(string: string, fret: number) {
    if (!this.synth) return;

    const guitarString = this.strings.find(s => s.name === string);
    if (!guitarString) return;

    const note = this.calculateNote(guitarString.baseNote, fret);
    this.synth.triggerAttackRelease(note, '8n');
  }

  private static calculateNote(baseNote: string, fret: number): string {
    const notes = ['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B'];
    const [note, octave] = baseNote.split(/(\d+)/);
    
    const baseIndex = notes.indexOf(note);
    const newIndex = (baseIndex + fret) % 12;
    const octaveShift = Math.floor((baseIndex + fret) / 12);
    
    return `${notes[newIndex]}${parseInt(octave) + octaveShift}`;
  }

  static setEffects(reverb: number, delay: number, chorus: number) {
    if (this.reverb) this.reverb.wet.value = reverb;
    if (this.delay) this.delay.wet.value = delay;
    if (this.chorus) this.chorus.wet.value = chorus;
  }
}