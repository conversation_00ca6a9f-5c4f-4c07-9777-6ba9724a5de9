/**
 * PDF Parser for Guitar Tab Player
 * Extracts tablature content from PDF files using PDF.js
 */

class PDFParser {
  static initialized = false;

  /**
   * Initialize PDF.js worker
   */
  static async initialize() {
    if (this.initialized) return;

    try {
      // Load PDF.js from CDN
      if (!window.pdfjsLib) {
        await this.loadPDFJS();
      }

      // Configure PDF.js worker with the correct version
      const version = '4.10.38'; // Match the installed version
      window.pdfjsLib.GlobalWorkerOptions.workerSrc = `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${version}/pdf.worker.min.js`;
      this.initialized = true;
      console.log('PDF.js initialized successfully');
    } catch (error) {
      console.error('Failed to load PDF.js:', error);
      throw new Error('PDF.js library could not be loaded. Please check your internet connection.');
    }
  }

  /**
   * Load PDF.js library dynamically
   */
  static async loadPDFJS() {
    return new Promise((resolve, reject) => {
      const script = document.createElement('script');
      const version = '4.10.38'; // Match the installed version
      script.src = `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${version}/pdf.min.js`;
      script.onload = () => {
        console.log('PDF.js library loaded from CDN');
        resolve();
      };
      script.onerror = (error) => {
        console.error('Failed to load PDF.js from CDN:', error);
        reject(error);
      };
      document.head.appendChild(script);
    });
  }

  /**
   * Extract tablature content from PDF file
   * @param {File} file - PDF file to process
   * @returns {Promise<string>} - Extracted tablature content
   */
  static async extractTabFromPDF(file) {
    try {
      await this.initialize();

      if (!window.pdfjsLib) {
        throw new Error('PDF.js library not available');
      }

      // Read file as ArrayBuffer
      const arrayBuffer = await file.arrayBuffer();

      // Load PDF document
      const pdf = await window.pdfjsLib.getDocument({ data: arrayBuffer }).promise;
    
    let tabContent = '';
    
    // Process each page
    for (let i = 1; i <= pdf.numPages; i++) {
      const page = await pdf.getPage(i);
      const textContent = await page.getTextContent();
      
      // Extract text content and analyze for tab patterns
      const pageText = textContent.items
        .map(item => item.str || '')
        .join('\n');
      
      // Look for tab patterns
      const tabLines = this.extractTabLines(pageText);
      
      if (tabLines.length > 0) {
        tabContent += tabLines.join('\n') + '\n\n';
      }
    }

      // Clean up and normalize tab content
      tabContent = this.normalizeTabContent(tabContent);

      return tabContent;
    } catch (error) {
      console.error('Error extracting PDF content:', error);

      // Fallback: suggest manual conversion
      throw new Error(`Failed to extract tablature from PDF: ${error.message}.

Suggestion: Try converting your PDF to text first, or use a simpler PDF with text-based tablature.`);
    }
  }

  /**
   * Extract lines that look like guitar tablature
   * @param {string} text - Text content from PDF page
   * @returns {Array<string>} - Array of tab lines
   */
  static extractTabLines(text) {
    const lines = text.split('\n');
    const tabLines = [];
    
    for (const line of lines) {
      const trimmedLine = line.trim();
      
      // Skip empty lines
      if (!trimmedLine) continue;
      
      // Look for standard tab patterns
      if (this.isTabLine(trimmedLine)) {
        tabLines.push(trimmedLine);
      }
    }
    
    return tabLines;
  }

  /**
   * Check if a line looks like guitar tablature
   * @param {string} line - Line to check
   * @returns {boolean} - True if line looks like tablature
   */
  static isTabLine(line) {
    // Check for standard tab format (e|---, B|---, etc.)
    if (/^[eEbBgGdDaA]\|/.test(line)) {
      return true;
    }
    
    // Check for lines with fret numbers and dashes/bars
    if (line.includes('|') && /[-0-9h~\\\/pb\(\)]{3,}/.test(line)) {
      return true;
    }
    
    // Check for lines that are mostly dashes and numbers
    const tabChars = line.match(/[-0-9|]/g);
    if (tabChars && tabChars.length > line.length * 0.6) {
      return true;
    }
    
    return false;
  }

  /**
   * Normalize and clean up tablature content
   * @param {string} content - Raw tablature content
   * @returns {string} - Cleaned tablature content
   */
  static normalizeTabContent(content) {
    return content
      // Remove extra whitespace around bars
      .replace(/\s+\|/g, '|')
      .replace(/\|\s+/g, '|')
      // Ensure consistent spacing
      .replace(/(-+)/g, (match) => match.replace(/-/g, '-'))
      // Remove empty lines and normalize line breaks
      .split('\n')
      .filter(line => line.trim().length > 0)
      .join('\n')
      // Add proper spacing between sections
      .replace(/(\n[eEbBgGdDaA]\|)/g, '\n$1');
  }

  /**
   * Analyze the structure of extracted tablature
   * @param {string} content - Tablature content
   * @returns {Object} - Analysis results
   */
  static analyzeTabStructure(content) {
    const lines = content.split('\n').filter(line => line.includes('|'));
    const stringCount = this.countUniqueStrings(lines);
    
    return {
      stringCount,
      timeSignature: this.detectTimeSignature(content),
      tempo: this.detectTempo(content),
      hasChords: this.detectChords(content),
      hasTablature: stringCount >= 4, // At least 4 strings for basic tab
      measures: this.countMeasures(content),
      totalLines: lines.length
    };
  }

  /**
   * Count unique string identifiers in tablature
   * @param {Array<string>} lines - Tab lines
   * @returns {number} - Number of unique strings
   */
  static countUniqueStrings(lines) {
    const strings = new Set();
    
    for (const line of lines) {
      const match = line.match(/^([eEbBgGdDaA])\|/);
      if (match) {
        strings.add(match[1].toLowerCase());
      }
    }
    
    return strings.size;
  }

  /**
   * Detect time signature in content
   * @param {string} content - Content to analyze
   * @returns {string} - Detected time signature
   */
  static detectTimeSignature(content) {
    const timeSignatureMatch = content.match(/(\d+\/\d+)/);
    return timeSignatureMatch ? timeSignatureMatch[1] : '4/4';
  }

  /**
   * Detect tempo in content
   * @param {string} content - Content to analyze
   * @returns {number|null} - Detected tempo or null
   */
  static detectTempo(content) {
    const tempoMatch = content.match(/(?:tempo|bpm)[=\s]+(\d+)/i);
    return tempoMatch ? parseInt(tempoMatch[1]) : null;
  }

  /**
   * Detect if content contains chord symbols
   * @param {string} content - Content to analyze
   * @returns {boolean} - True if chords detected
   */
  static detectChords(content) {
    const chordPattern = /[A-G][#b]?(maj|min|m|M|aug|dim|7|9|13)/;
    return chordPattern.test(content);
  }

  /**
   * Count measures in tablature
   * @param {string} content - Content to analyze
   * @returns {number} - Number of measures
   */
  static countMeasures(content) {
    const lines = content.split('\n');
    let maxMeasures = 0;
    
    for (const line of lines) {
      if (line.includes('|')) {
        const measures = (line.match(/\|/g) || []).length - 1;
        maxMeasures = Math.max(maxMeasures, measures);
      }
    }
    
    return Math.max(maxMeasures, 1);
  }
}

// Make PDFParser available globally
window.PDFParser = PDFParser;
