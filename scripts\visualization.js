/**
 * Visualization for Guitar Tab Player
 * Creates interactive visualizations of guitar tablature
 */

class TabVisualization {
  constructor() {
    this.container = null;
    this.strings = ['e', 'B', 'G', 'D', 'A', 'E'];
    this.stringElements = [];
    this.fretMarkers = [];
    this.isInitialized = false;
  }
  
  /**
   * Initialize the visualization container
   * @param {string} containerId - ID of the container element
   */
  initialize(containerId) {
    if (this.isInitialized) return;
    
    this.container = document.getElementById(containerId);
    if (!this.container) {
      console.error(`Container element with ID '${containerId}' not found`);
      return;
    }
    
    // Clear any existing content
    this.container.innerHTML = '';
    
    // Create string elements
    this.createGuitarStrings();
    
    // Create fret markers
    this.createFretMarkers();
    
    this.isInitialized = true;
  }
  
  /**
   * Create visual representation of guitar strings
   */
  createGuitarStrings() {
    // Define string thicknesses (px)
    const stringThicknesses = [1, 2, 3, 4, 5, 6];
    
    // Calculate spacing between strings
    const containerHeight = this.container.clientHeight;
    const spacing = containerHeight / 7; // 7 spaces for 6 strings
    
    // Create each string
    this.strings.forEach((stringName, index) => {
      const stringElement = document.createElement('div');
      stringElement.className = 'guitar-string-viz';
      stringElement.dataset.string = stringName;
      
      // Position the string
      stringElement.style.top = `${spacing * (index + 1)}px`;
      stringElement.style.height = `${stringThicknesses[index]}px`;
      
      // Add string label
      const label = document.createElement('div');
      label.className = 'string-label';
      label.textContent = stringName;
      label.style.position = 'absolute';
      label.style.left = '5px';
      label.style.top = `${-10}px`;
      label.style.fontSize = '12px';
      label.style.color = 'var(--text-secondary)';
      
      stringElement.appendChild(label);
      this.container.appendChild(stringElement);
      this.stringElements.push(stringElement);
    });
  }
  
  /**
   * Create fret markers along the neck
   */
  createFretMarkers() {
    // Clear existing markers
    this.fretMarkers = [];
    
    // Create container for fret markers
    const markersContainer = document.createElement('div');
    markersContainer.className = 'fret-markers';
    markersContainer.style.position = 'absolute';
    markersContainer.style.top = '0';
    markersContainer.style.left = '0';
    markersContainer.style.right = '0';
    markersContainer.style.bottom = '0';
    markersContainer.style.pointerEvents = 'none';
    
    // Add fret markers
    const containerWidth = this.container.clientWidth;
    const numFrets = 12; // Display 12 frets
    const fretWidth = containerWidth / (numFrets + 1);
    
    for (let i = 1; i <= numFrets; i++) {
      const marker = document.createElement('div');
      marker.className = 'fret-marker';
      marker.style.position = 'absolute';
      marker.style.left = `${i * fretWidth}px`;
      marker.style.top = '0';
      marker.style.bottom = '0';
      marker.style.width = '2px';
      marker.style.backgroundColor = 'rgba(var(--border-rgb), 0.3)';
      
      // Add fret number
      const fretNumber = document.createElement('div');
      fretNumber.className = 'fret-number';
      fretNumber.textContent = i;
      fretNumber.style.position = 'absolute';
      fretNumber.style.bottom = '5px';
      fretNumber.style.left = '-8px';
      fretNumber.style.fontSize = '10px';
      fretNumber.style.color = 'var(--text-secondary)';
      
      marker.appendChild(fretNumber);
      markersContainer.appendChild(marker);
      this.fretMarkers.push(marker);
    }
    
    this.container.appendChild(markersContainer);
  }
  
  /**
   * Visualize a note being played
   * @param {Object} noteData - Note data from the parser
   */
  visualizeNote(noteData) {
    if (!this.isInitialized) return;
    
    // Clear previous visualizations
    this.clearActiveVisualizations();
    
    // Visualize each note in the chord
    noteData.notes.forEach(note => {
      // Find the string element
      const stringElement = this.stringElements.find(el => 
        el.dataset.string === note.string
      );
      
      if (!stringElement) return;
      
      // Show the note on the string
      this.pluckString(stringElement);
      
      // Highlight the fret position
      this.highlightFret(note.fret);
    });
  }
  
  /**
   * Animate a string being plucked
   * @param {HTMLElement} stringElement - String element to animate
   */
  pluckString(stringElement) {
    // Create a plucked note indicator
    const noteIndicator = document.createElement('div');
    noteIndicator.className = 'note-indicator';
    noteIndicator.style.position = 'absolute';
    noteIndicator.style.width = '16px';
    noteIndicator.style.height = '16px';
    noteIndicator.style.borderRadius = '50%';
    noteIndicator.style.backgroundColor = 'var(--accent)';
    noteIndicator.style.left = '50px'; // Position along the string
    noteIndicator.style.top = '50%';
    noteIndicator.style.transform = 'translate(-50%, -50%)';
    noteIndicator.style.boxShadow = '0 0 10px var(--accent)';
    noteIndicator.style.opacity = '0.8';
    noteIndicator.style.animation = 'highlight 0.6s ease-in-out';
    
    stringElement.appendChild(noteIndicator);
    
    // Add plucked animation class
    stringElement.classList.add('plucked');
    
    // Remove animation and indicator after it completes
    setTimeout(() => {
      stringElement.classList.remove('plucked');
      if (noteIndicator.parentNode) {
        noteIndicator.parentNode.removeChild(noteIndicator);
      }
    }, 800);
  }
  
  /**
   * Highlight a specific fret
   * @param {number} fret - Fret number to highlight
   */
  highlightFret(fret) {
    if (fret <= 0 || fret > this.fretMarkers.length) return;
    
    // Get the fret marker
    const fretMarker = this.fretMarkers[fret - 1];
    
    // Create a highlight effect
    const highlight = document.createElement('div');
    highlight.className = 'fret-highlight';
    highlight.style.position = 'absolute';
    highlight.style.width = '100%';
    highlight.style.height = '100%';
    highlight.style.backgroundColor = 'rgba(var(--accent-rgb), 0.2)';
    highlight.style.animation = 'markerPulse 0.5s ease-in-out';
    highlight.style.pointerEvents = 'none';
    
    fretMarker.appendChild(highlight);
    fretMarker.classList.add('active');
    
    // Remove highlight after animation
    setTimeout(() => {
      fretMarker.classList.remove('active');
      if (highlight.parentNode) {
        highlight.parentNode.removeChild(highlight);
      }
    }, 500);
  }
  
  /**
   * Clear all active visualizations
   */
  clearActiveVisualizations() {
    // Remove all note indicators
    const indicators = this.container.querySelectorAll('.note-indicator');
    indicators.forEach(indicator => {
      indicator.parentNode.removeChild(indicator);
    });
    
    // Remove all fret highlights
    const highlights = this.container.querySelectorAll('.fret-highlight');
    highlights.forEach(highlight => {
      highlight.parentNode.removeChild(highlight);
    });
    
    // Remove active classes
    this.stringElements.forEach(string => {
      string.classList.remove('plucked');
    });
    
    this.fretMarkers.forEach(marker => {
      marker.classList.remove('active');
    });
  }
  
  /**
   * Resize the visualization when container size changes
   */
  resize() {
    if (!this.isInitialized) return;
    
    // Clear existing visualization
    this.container.innerHTML = '';
    this.stringElements = [];
    this.fretMarkers = [];
    
    // Recreate visualization
    this.createGuitarStrings();
    this.createFretMarkers();
  }
}

// Create global instance of tab visualization
const tabVisualization = new TabVisualization();