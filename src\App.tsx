import React, { useState, useEffect } from 'react';
import { Guitar, Music, Settings, Sun, Moon, History } from 'lucide-react';
import { TabEditor } from './components/TabEditor';
import { ThreeScene } from './components/ThreeScene';
import { Changelog } from './components/Changelog';
import { AudioEngine } from './lib/AudioEngine';
import { useTheme } from './hooks/useTheme';

function App() {
  const [isPlaying, setIsPlaying] = useState(false);
  const [showChangelog, setShowChangelog] = useState(false);
  const { theme, toggleTheme } = useTheme();
  const [audioSettings, setAudioSettings] = useState({
    guitarType: 'acoustic',
    reverb: 0.3,
    delay: 0.2,
    chorus: 0.1
  });

  useEffect(() => {
    AudioEngine.initialize();
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 to-purple-900 dark:from-black dark:to-purple-950 text-white">
      <nav className="fixed top-0 w-full bg-black/30 backdrop-blur-lg border-b border-white/10 z-50">
        <div className="max-w-7xl mx-auto px-4 py-3 flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Guitar className="w-8 h-8 text-purple-400" />
            <h1 className="text-xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
              Guitar Tab Player
            </h1>
          </div>
          
          <div className="flex items-center gap-4">
            <button 
              onClick={() => setShowChangelog(true)}
              className="flex items-center gap-2 px-3 py-1.5 rounded-lg bg-white/5 hover:bg-white/10 transition-colors"
            >
              <History className="w-4 h-4" />
              <span className="text-sm">Changelog</span>
            </button>
            
            <button
              onClick={toggleTheme}
              className="p-2 rounded-lg bg-white/5 hover:bg-white/10 transition-colors"
            >
              {theme === 'dark' ? <Sun className="w-5 h-5" /> : <Moon className="w-5 h-5" />}
            </button>
          </div>
        </div>
      </nav>

      <main className="pt-20 pb-10 px-4">
        <div className="max-w-7xl mx-auto grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div className="space-y-6">
            <div className="relative h-[300px] rounded-2xl overflow-hidden border border-white/10">
              <ThreeScene isPlaying={isPlaying} />
            </div>

            <div className="bg-black/30 backdrop-blur rounded-2xl border border-white/10 p-6">
              <TabEditor 
                isPlaying={isPlaying}
                onPlay={() => setIsPlaying(true)}
                onStop={() => setIsPlaying(false)}
              />
            </div>
          </div>

          <div className="space-y-6">
            <div className="bg-black/30 backdrop-blur rounded-2xl border border-white/10 p-6">
              <div className="flex items-center gap-3 mb-6">
                <Settings className="w-5 h-5 text-purple-400" />
                <h2 className="text-lg font-semibold">Sound Settings</h2>
              </div>

              <div className="space-y-4">
                <div className="space-y-2">
                  <label className="text-sm text-gray-300">Guitar Type</label>
                  <select 
                    value={audioSettings.guitarType}
                    onChange={(e) => setAudioSettings(prev => ({ ...prev, guitarType: e.target.value }))}
                    className="w-full bg-white/5 border border-white/10 rounded-lg px-3 py-2 text-sm"
                  >
                    <option value="acoustic">Acoustic</option>
                    <option value="electric">Electric</option>
                    <option value="classical">Classical</option>
                  </select>
                </div>

                <div className="space-y-2">
                  <label className="text-sm text-gray-300">Reverb</label>
                  <input 
                    type="range"
                    min="0"
                    max="1"
                    step="0.1"
                    value={audioSettings.reverb}
                    onChange={(e) => setAudioSettings(prev => ({ ...prev, reverb: parseFloat(e.target.value) }))}
                    className="w-full"
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-sm text-gray-300">Delay</label>
                  <input 
                    type="range"
                    min="0"
                    max="1"
                    step="0.1"
                    value={audioSettings.delay}
                    onChange={(e) => setAudioSettings(prev => ({ ...prev, delay: parseFloat(e.target.value) }))}
                    className="w-full"
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-sm text-gray-300">Chorus</label>
                  <input 
                    type="range"
                    min="0"
                    max="1"
                    step="0.1"
                    value={audioSettings.chorus}
                    onChange={(e) => setAudioSettings(prev => ({ ...prev, chorus: parseFloat(e.target.value) }))}
                    className="w-full"
                  />
                </div>
              </div>
            </div>

            <div className="bg-black/30 backdrop-blur rounded-2xl border border-white/10 p-6">
              <div className="flex items-center gap-3 mb-6">
                <Music className="w-5 h-5 text-purple-400" />
                <h2 className="text-lg font-semibold">Visualization</h2>
              </div>

              <div className="relative h-[200px] rounded-lg overflow-hidden border border-white/10">
                {/* Guitar neck visualization will go here */}
              </div>
            </div>
          </div>
        </div>
      </main>

      {showChangelog && (
        <Changelog onClose={() => setShowChangelog(false)} />
      )}
    </div>
  );
}

export default App;