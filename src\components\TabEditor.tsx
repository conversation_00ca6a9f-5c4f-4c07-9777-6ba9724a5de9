import React, { useState, useRef, useCallback } from 'react';
import { Play, Square, Upload, FileText, FileCode } from 'lucide-react';
import { TabParser } from '../lib/TabParser';
import { PDFParser } from '../lib/PDFParser';
import { AudioEngine } from '../lib/AudioEngine';

interface TabEditorProps {
  isPlaying: boolean;
  onPlay: () => void;
  onStop: () => void;
}

export function TabEditor({ isPlaying, onPlay, onStop }: TabEditorProps) {
  const [tabContent, setTabContent] = useState('');
  const [currentPosition, setCurrentPosition] = useState(0);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsProcessing(true);
    setError(null);

    try {
      let content = '';
      
      if (file.type === 'application/pdf') {
        content = await PDFParser.extractTabFromPDF(file);
        
        // Analyze the extracted content
        const analysis = await PDFParser.analyzeTabStructure(content);
        
        if (!analysis.hasTablature) {
          throw new Error('No valid tablature found in PDF');
        }
        
        // Update audio engine with detected tempo if available
        if (analysis.tempo) {
          AudioEngine.setTempo(analysis.tempo);
        }
      } else {
        // Handle text files
        content = await file.text();
      }

      setTabContent(content);
      TabParser.parse(content); // Validate the content
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to process file');
    } finally {
      setIsProcessing(false);
    }
  };

  const handlePlay = useCallback(() => {
    if (!tabContent.trim()) return;
    onPlay();
    TabParser.startPlayback(tabContent, (position) => {
      setCurrentPosition(position);
    });
  }, [tabContent, onPlay]);

  const handleStop = useCallback(() => {
    onStop();
    TabParser.stopPlayback();
    setCurrentPosition(0);
  }, [onStop]);

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <button
            onClick={handlePlay}
            disabled={isPlaying || !tabContent.trim() || isProcessing}
            className="p-2 rounded-lg bg-purple-600 hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            title="Play"
          >
            <Play className="w-5 h-5" />
          </button>
          
          <button
            onClick={handleStop}
            disabled={!isPlaying}
            className="p-2 rounded-lg bg-red-600 hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            title="Stop"
          >
            <Square className="w-5 h-5" />
          </button>
        </div>

        <div className="flex items-center gap-2">
          <button
            onClick={() => fileInputRef.current?.click()}
            disabled={isProcessing}
            className="flex items-center gap-2 px-3 py-1.5 rounded-lg bg-white/5 hover:bg-white/10 transition-colors disabled:opacity-50"
          >
            <Upload className="w-4 h-4" />
            <span className="text-sm">Upload Tab</span>
            <span className="text-xs text-gray-400">(TXT, PDF)</span>
          </button>
        </div>
        
        <input
          ref={fileInputRef}
          type="file"
          accept=".txt,.pdf"
          onChange={handleFileUpload}
          className="hidden"
        />
      </div>

      {isProcessing && (
        <div className="flex items-center justify-center py-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
        </div>
      )}

      {error && (
        <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4 text-red-400 text-sm">
          {error}
        </div>
      )}

      <div className="relative">
        <pre className="font-mono text-sm bg-black/20 rounded-lg p-4 overflow-x-auto whitespace-pre">
          {TabParser.highlightPosition(tabContent, currentPosition)}
        </pre>
      </div>
    </div>
  );
}