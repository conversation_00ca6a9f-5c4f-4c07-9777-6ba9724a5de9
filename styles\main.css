/* Main styles for Guitar Tab Player */
:root {
  /* Light theme colors */
  --light-bg-primary: #f8fafc;
  --light-bg-secondary: #ffffff;
  --light-text-primary: #1e293b;
  --light-text-secondary: #64748b;
  --light-accent: #6366f1;
  --light-accent-hover: #4f46e5;
  --light-border: #e2e8f0;
  --light-card-bg: #ffffff;
  --light-card-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
  --light-tab-bg: #f1f5f9;
  --light-progress-bg: #e2e8f0;
  --light-progress-fill: #6366f1;
  --light-button-text: #ffffff;
  
  /* Dark theme colors */
  --dark-bg-primary: #0f172a;
  --dark-bg-secondary: #1e293b;
  --dark-text-primary: #f1f5f9;
  --dark-text-secondary: #94a3b8;
  --dark-accent: #6366f1;
  --dark-accent-hover: #7c3aed;
  --dark-border: #334155;
  --dark-card-bg: #1e293b;
  --dark-card-shadow: 0 4px 16px rgba(0, 0, 0, 0.25);
  --dark-tab-bg: #334155;
  --dark-progress-bg: #475569;
  --dark-progress-fill: #6366f1;
  --dark-button-text: #ffffff;

  /* Common variables */
  --font-family-main: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-family-mono: 'JetBrains Mono', 'SFMono-Regular', Consolas, monospace;
  --border-radius-sm: 6px;
  --border-radius-md: 8px;
  --border-radius-lg: 12px;
  --transition-fast: 200ms ease;
  --transition-normal: 300ms ease;
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
}

/* Theme variables assignment */
.theme-light {
  --bg-primary: var(--light-bg-primary);
  --bg-secondary: var(--light-bg-secondary);
  --text-primary: var(--light-text-primary);
  --text-secondary: var(--light-text-secondary);
  --accent: var(--light-accent);
  --accent-hover: var(--light-accent-hover);
  --border: var(--light-border);
  --card-bg: var(--light-card-bg);
  --card-shadow: var(--light-card-shadow);
  --tab-bg: var(--light-tab-bg);
  --progress-bg: var(--light-progress-bg);
  --progress-fill: var(--light-progress-fill);
  --button-text: var(--light-button-text);
}

.theme-dark {
  --bg-primary: var(--dark-bg-primary);
  --bg-secondary: var(--dark-bg-secondary);
  --text-primary: var(--dark-text-primary);
  --text-secondary: var(--dark-text-secondary);
  --accent: var(--dark-accent);
  --accent-hover: var(--dark-accent-hover);
  --border: var(--dark-border);
  --card-bg: var(--dark-card-bg);
  --card-shadow: var(--dark-card-shadow);
  --tab-bg: var(--dark-tab-bg);
  --progress-bg: var(--dark-progress-bg);
  --progress-fill: var(--dark-progress-fill);
  --button-text: var(--dark-button-text);
}

/* Base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-size: 16px;
}

body {
  font-family: var(--font-family-main);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  line-height: 1.5;
  transition: background-color var(--transition-normal), color var(--transition-normal);
}

.app-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-md);
}

/* Header */
.app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md) 0;
  margin-bottom: var(--spacing-lg);
}

.logo-container {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.logo-icon {
  position: relative;
  width: 32px;
  height: 32px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.guitar-string {
  display: block;
  height: 2px;
  background-color: var(--accent);
  border-radius: 1px;
}

.logo-container h1 {
  font-size: 1.5rem;
  font-weight: 700;
}

/* Main content */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

.section-header {
  margin-bottom: var(--spacing-md);
}

.section-header h2 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
}

/* Footer */
.app-footer {
  margin-top: var(--spacing-xl);
  padding: var(--spacing-md) 0;
  text-align: center;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .app-container {
    padding: var(--spacing-sm);
  }
  
  .main-content {
    gap: var(--spacing-lg);
  }
  
  .logo-container h1 {
    font-size: 1.25rem;
  }
  
  .section-header h2 {
    font-size: 1.125rem;
  }
}

@media (max-width: 480px) {
  .app-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-md);
  }
  
  .header-controls {
    align-self: flex-end;
  }
}