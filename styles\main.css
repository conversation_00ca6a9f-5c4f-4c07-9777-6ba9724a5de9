/* Main styles for Guitar Tab Player */
:root {
  /* Light theme colors */
  --light-bg-primary: #f8f9fa;
  --light-bg-secondary: #ffffff;
  --light-text-primary: #212529;
  --light-text-secondary: #495057;
  --light-accent: #6c5ce7;
  --light-accent-hover: #5549d1;
  --light-border: #dee2e6;
  --light-card-bg: #ffffff;
  --light-card-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  --light-tab-bg: #f1f3f5;
  --light-progress-bg: #e9ecef;
  --light-progress-fill: #6c5ce7;
  --light-button-text: #ffffff;
  
  /* Dark theme colors */
  --dark-bg-primary: #131620;
  --dark-bg-secondary: #1a1f2e;
  --dark-text-primary: #e9ecef;
  --dark-text-secondary: #adb5bd;
  --dark-accent: #6c5ce7;
  --dark-accent-hover: #8477e9;
  --dark-border: #2a2f3f;
  --dark-card-bg: #1a1f2e;
  --dark-card-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  --dark-tab-bg: #232836;
  --dark-progress-bg: #2a2f3f;
  --dark-progress-fill: #6c5ce7;
  --dark-button-text: #ffffff;

  /* Common variables */
  --font-family-main: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-family-mono: 'JetBrains Mono', 'SFMono-Regular', Consolas, monospace;
  --border-radius-sm: 6px;
  --border-radius-md: 8px;
  --border-radius-lg: 12px;
  --transition-fast: 200ms ease;
  --transition-normal: 300ms ease;
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
}

/* Theme variables assignment */
.theme-light {
  --bg-primary: var(--light-bg-primary);
  --bg-secondary: var(--light-bg-secondary);
  --text-primary: var(--light-text-primary);
  --text-secondary: var(--light-text-secondary);
  --accent: var(--light-accent);
  --accent-hover: var(--light-accent-hover);
  --border: var(--light-border);
  --card-bg: var(--light-card-bg);
  --card-shadow: var(--light-card-shadow);
  --tab-bg: var(--light-tab-bg);
  --progress-bg: var(--light-progress-bg);
  --progress-fill: var(--light-progress-fill);
  --button-text: var(--light-button-text);
}

.theme-dark {
  --bg-primary: var(--dark-bg-primary);
  --bg-secondary: var(--dark-bg-secondary);
  --text-primary: var(--dark-text-primary);
  --text-secondary: var(--dark-text-secondary);
  --accent: var(--dark-accent);
  --accent-hover: var(--dark-accent-hover);
  --border: var(--dark-border);
  --card-bg: var(--dark-card-bg);
  --card-shadow: var(--dark-card-shadow);
  --tab-bg: var(--dark-tab-bg);
  --progress-bg: var(--dark-progress-bg);
  --progress-fill: var(--dark-progress-fill);
  --button-text: var(--dark-button-text);
}

/* Base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-size: 16px;
}

body {
  font-family: var(--font-family-main);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  line-height: 1.5;
  transition: background-color var(--transition-normal), color var(--transition-normal);
}

.app-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-md);
}

/* Header */
.app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md) 0;
  margin-bottom: var(--spacing-lg);
}

.logo-container {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.logo-icon {
  position: relative;
  width: 32px;
  height: 32px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.guitar-string {
  display: block;
  height: 2px;
  background-color: var(--accent);
  border-radius: 1px;
}

.logo-container h1 {
  font-size: 1.5rem;
  font-weight: 700;
}

/* Main content */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

.section-header {
  margin-bottom: var(--spacing-md);
}

.section-header h2 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
}

/* Footer */
.app-footer {
  margin-top: var(--spacing-xl);
  padding: var(--spacing-md) 0;
  text-align: center;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .app-container {
    padding: var(--spacing-sm);
  }
  
  .main-content {
    gap: var(--spacing-lg);
  }
  
  .logo-container h1 {
    font-size: 1.25rem;
  }
  
  .section-header h2 {
    font-size: 1.125rem;
  }
}

@media (max-width: 480px) {
  .app-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-md);
  }
  
  .header-controls {
    align-self: flex-end;
  }
}