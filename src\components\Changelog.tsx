import React from 'react';
import { X } from 'lucide-react';

interface ChangelogProps {
  onClose: () => void;
}

export function Changelog({ onClose }: ChangelogProps) {
  const changes = [
    {
      version: '1.1.0',
      date: '2025-03-15',
      changes: [
        '🎸 Added 3D visualization effects',
        '✨ New sound effects: Chorus and Delay',
        '🎨 Redesigned interface with glass morphism',
        '🌗 Added dark/light theme support'
      ]
    },
    {
      version: '1.0.1',
      date: '2025-03-01',
      changes: [
        '🔧 Improved audio engine performance',
        '🐛 Fixed timing issues in tab playback',
        '📱 Better mobile responsiveness'
      ]
    },
    {
      version: '1.0.0',
      date: '2025-02-15',
      changes: [
        '🚀 Initial release',
        '🎵 Basic tab playback support',
        '📝 Tab editor with syntax highlighting',
        '🎚️ Speed and volume controls'
      ]
    }
  ];

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50">
      <div className="bg-gray-900 border border-white/10 rounded-2xl w-full max-w-lg max-h-[80vh] overflow-hidden">
        <div className="p-6 border-b border-white/10 flex items-center justify-between">
          <h2 className="text-xl font-semibold">Changelog</h2>
          <button
            onClick={onClose}
            className="p-2 rounded-lg hover:bg-white/5 transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        <div className="p-6 overflow-y-auto">
          {changes.map((release) => (
            <div key={release.version} className="mb-8 last:mb-0">
              <div className="flex items-baseline gap-3 mb-3">
                <h3 className="text-lg font-semibold text-purple-400">
                  v{release.version}
                </h3>
                <span className="text-sm text-gray-400">{release.date}</span>
              </div>
              <ul className="space-y-2">
                {release.changes.map((change, index) => (
                  <li key={index} className="flex items-baseline gap-2 text-gray-300">
                    <span className="text-sm">{change}</span>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}