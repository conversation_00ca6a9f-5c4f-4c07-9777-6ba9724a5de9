/* Animations for Guitar Tab Player */

/* Theme transition */
body, .card, .tab-textarea-container, .tab-display, .guitar-neck {
  transition: background-color 0.5s ease, color 0.5s ease, border-color 0.5s ease, box-shadow 0.5s ease;
}

/* Logo strings animation */
@keyframes pluck {
  0% {
    transform: scaleY(1);
    opacity: 1;
  }
  50% {
    transform: scaleY(1.5);
    opacity: 0.8;
  }
  100% {
    transform: scaleY(1);
    opacity: 1;
  }
}

.guitar-string:nth-child(1) { animation: pluck 1.2s ease-in-out 0.1s infinite; }
.guitar-string:nth-child(2) { animation: pluck 1.5s ease-in-out 0.2s infinite; }
.guitar-string:nth-child(3) { animation: pluck 1.3s ease-in-out 0.3s infinite; }
.guitar-string:nth-child(4) { animation: pluck 1.7s ease-in-out 0.4s infinite; }
.guitar-string:nth-child(5) { animation: pluck 1.4s ease-in-out 0.5s infinite; }
.guitar-string:nth-child(6) { animation: pluck 1.6s ease-in-out 0.6s infinite; }

/* Play/Stop button hover effect */
.circle-button:hover {
  box-shadow: 0 0 15px var(--accent);
}

/* Note highlight animation */
@keyframes highlight {
  0% {
    background-color: rgba(108, 92, 231, 0.2);
  }
  50% {
    background-color: rgba(108, 92, 231, 0.5);
  }
  100% {
    background-color: rgba(108, 92, 231, 0.2);
  }
}

.note-highlight {
  animation: highlight 0.6s ease-in-out infinite;
}

/* String pluck animation for visualization */
@keyframes pluckString {
  0% {
    transform: translateY(0);
    opacity: 1;
  }
  50% {
    transform: translateY(4px);
    opacity: 0.7;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

.string-pluck {
  animation: pluckString 0.3s ease-in-out;
}

/* Progress bar pulse when playing */
@keyframes progressPulse {
  0% {
    opacity: 0.8;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.8;
  }
}

.progress-fill.active {
  animation: progressPulse 2s ease-in-out infinite;
}

/* Fade in for newly loaded content */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.5s ease-out forwards;
}

/* Button press effect */
.button:active, .circle-button:active {
  transform: scale(0.95);
}

/* Tab display loading effect */
@keyframes loadingPulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}

.loading {
  animation: loadingPulse 1.5s ease-in-out infinite;
}

/* Guitar string visualization animation */
.guitar-string-viz {
  position: absolute;
  left: 0;
  right: 0;
  height: 2px;
  background-color: var(--accent);
  transform-origin: left center;
}

.guitar-string-viz.plucked {
  animation: stringVibration 0.8s ease-out;
}

@keyframes stringVibration {
  0% {
    transform: scaleY(1);
  }
  10% {
    transform: scaleY(3);
  }
  20% {
    transform: scaleY(2);
  }
  30% {
    transform: scaleY(2.5);
  }
  40% {
    transform: scaleY(1.5);
  }
  50% {
    transform: scaleY(2);
  }
  60% {
    transform: scaleY(1.5);
  }
  70% {
    transform: scaleY(1.2);
  }
  80% {
    transform: scaleY(1.1);
  }
  100% {
    transform: scaleY(1);
  }
}

/* Fretboard markers animation */
@keyframes markerPulse {
  0% {
    transform: scale(1);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0.7;
  }
}

.fret-marker.active {
  animation: markerPulse 0.5s ease-in-out;
}

/* Current note highlight */
.current-note {
  background-color: rgba(108, 92, 231, 0.3);
  color: var(--accent);
  border-radius: 3px;
  display: inline-block;
  padding: 0 4px;
}