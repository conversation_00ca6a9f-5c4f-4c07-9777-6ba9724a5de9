{"name": "guitar-tab-player", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@tiptap/core": "^2.2.4", "@tiptap/pm": "^2.2.4", "@tiptap/starter-kit": "^2.2.4", "gsap": "^3.12.5", "lucide-react": "^0.344.0", "pdfjs-dist": "^4.10.38", "react": "^18.3.1", "react-dom": "^18.3.1", "three": "^0.162.0", "tone": "^14.7.77"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@types/three": "^0.162.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}