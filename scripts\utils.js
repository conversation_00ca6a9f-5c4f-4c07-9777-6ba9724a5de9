/**
 * Utility functions for Guitar Tab Player
 */

const Utils = {
  /**
   * Safely get an element by ID with error handling
   * @param {string} id - Element ID
   * @returns {HTMLElement} - DOM Element
   */
  getElement(id) {
    const element = document.getElementById(id);
    if (!element) {
      console.error(`Element with ID '${id}' not found`);
    }
    return element;
  },

  /**
   * Format a number to a specific decimal place
   * @param {number} value - Number to format
   * @param {number} decimals - Number of decimal places
   * @returns {string} - Formatted number
   */
  formatNumber(value, decimals = 1) {
    return value.toFixed(decimals);
  },

  /**
   * Toggle between light and dark themes
   */
  toggleTheme() {
    const body = document.body;
    if (body.classList.contains('theme-dark')) {
      body.classList.remove('theme-dark');
      body.classList.add('theme-light');
      localStorage.setItem('theme', 'light');
    } else {
      body.classList.remove('theme-light');
      body.classList.add('theme-dark');
      localStorage.setItem('theme', 'dark');
    }
  },

  /**
   * Load saved theme from localStorage or use system preference
   */
  loadSavedTheme() {
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme) {
      document.body.className = `theme-${savedTheme}`;
    } else {
      // Check for system dark mode preference
      if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
        document.body.className = 'theme-dark';
      } else {
        document.body.className = 'theme-light';
      }
    }
  },

  /**
   * Add a class to an element for a set duration, then remove it
   * @param {HTMLElement} element - DOM element
   * @param {string} className - Class to add
   * @param {number} duration - Duration in ms
   */
  addTemporaryClass(element, className, duration = 1000) {
    if (!element) return;
    element.classList.add(className);
    setTimeout(() => {
      element.classList.remove(className);
    }, duration);
  },

  /**
   * Format milliseconds to MM:SS format
   * @param {number} ms - Milliseconds
   * @returns {string} - Formatted time
   */
  formatTime(ms) {
    const totalSeconds = Math.floor(ms / 1000);
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  },

  /**
   * Debounce function to limit function calls
   * @param {Function} func - Function to debounce
   * @param {number} wait - Wait time in ms
   * @returns {Function} - Debounced function
   */
  debounce(func, wait = 300) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  },

  /**
   * Add a fade-in animation to an element
   * @param {HTMLElement} element - DOM element
   */
  fadeIn(element) {
    if (!element) return;
    element.classList.add('fade-in');
    setTimeout(() => {
      element.classList.remove('fade-in');
    }, 500);
  },

  /**
   * Update text content of an element with animation
   * @param {HTMLElement} element - DOM element
   * @param {string} text - New text content
   */
  updateTextWithAnimation(element, text) {
    if (!element) return;
    element.classList.add('fade-in');
    element.textContent = text;
    setTimeout(() => {
      element.classList.remove('fade-in');
    }, 500);
  },

  /**
   * Check if Web Audio API is supported
   * @returns {boolean} - True if supported
   */
  isWebAudioSupported() {
    return !!(window.AudioContext || window.webkitAudioContext);
  },

  /**
   * Show a status message to the user
   * @param {string} message - Message to display
   * @param {string} type - Message type (default, playing, stopped, loading, success, error)
   */
  showStatus(message, type = 'default') {
    const statusElement = this.getElement('status');
    if (!statusElement) return;

    statusElement.textContent = message;
    statusElement.className = 'status-text';

    if (type !== 'default') {
      statusElement.classList.add(`status-${type}`);
    }

    this.fadeIn(statusElement);

    // Auto-clear success and error messages after 3 seconds
    if (type === 'success' || type === 'error') {
      setTimeout(() => {
        if (statusElement.classList.contains(`status-${type}`)) {
          this.showStatus('Ready to play', 'default');
        }
      }, 3000);
    }
  },

  /**
   * Show a notification toast
   * @param {string} message - Message to display
   * @param {string} type - Notification type (success, error, info)
   * @param {number} duration - Duration in ms
   */
  showNotification(message, type = 'info', duration = 3000) {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;

    // Add to page
    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => notification.classList.add('show'), 100);

    // Remove after duration
    setTimeout(() => {
      notification.classList.remove('show');
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 300);
    }, duration);
  }
};

// Initialize theme
document.addEventListener('DOMContentLoaded', () => {
  Utils.loadSavedTheme();
});