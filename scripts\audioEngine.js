/**
 * Advanced Audio Engine for Guitar Tab Player
 * Uses Web Audio API to create realistic guitar sounds
 */

class AudioEngine {
  constructor() {
    this.initialized = false;
    this.context = null;
    this.masterGain = null;
    this.reverbNode = null;
    this.compressor = null;
    this.volume = 0.5;
    this.guitarType = 'acoustic';
    
    // Note frequencies mapping
    this.noteFrequencies = {
      'E2': 82.41, 'F2': 87.31, 'F#2': 92.50, 'G2': 98.00, 'G#2': 103.83, 
      'A2': 110.00, 'A#2': 116.54, 'B2': 123.47, 'C3': 130.81, 'C#3': 138.59,
      'D3': 146.83, 'D#3': 155.56, 'E3': 164.81, 'F3': 174.61, 'F#3': 185.00,
      'G3': 196.00, 'G#3': 207.65, 'A3': 220.00, 'A#3': 233.08, 'B3': 246.94,
      'C4': 261.63, 'C#4': 277.18, 'D4': 293.66, 'D#4': 311.13, 'E4': 329.63,
      'F4': 349.23, 'F#4': 369.99, 'G4': 392.00, 'G#4': 415.30, 'A4': 440.00,
      'A#4': 466.16, 'B4': 493.88, 'C5': 523.25, 'C#5': 554.37, 'D5': 587.33,
      'D#5': 622.25, 'E5': 659.25, 'F5': 698.46, 'F#5': 739.99, 'G5': 783.99
    };
    
    // Guitar string to note mapping
    this.stringNotes = {
      'e': ['E4', 'F4', 'F#4', 'G4', 'G#4', 'A4', 'A#4', 'B4', 'C5', 'C#5', 'D5', 'D#5', 'E5', 'F5', 'F#5', 'G5', 'G#5', 'A5', 'A#5', 'B5'],
      'B': ['B3', 'C4', 'C#4', 'D4', 'D#4', 'E4', 'F4', 'F#4', 'G4', 'G#4', 'A4', 'A#4', 'B4', 'C5', 'C#5', 'D5', 'D#5', 'E5', 'F5', 'F#5'],
      'G': ['G3', 'G#3', 'A3', 'A#3', 'B3', 'C4', 'C#4', 'D4', 'D#4', 'E4', 'F4', 'F#4', 'G4', 'G#4', 'A4', 'A#4', 'B4', 'C5', 'C#5', 'D5'],
      'D': ['D3', 'D#3', 'E3', 'F3', 'F#3', 'G3', 'G#3', 'A3', 'A#3', 'B3', 'C4', 'C#4', 'D4', 'D#4', 'E4', 'F4', 'F#4', 'G4', 'G#4', 'A4'],
      'A': ['A2', 'A#2', 'B2', 'C3', 'C#3', 'D3', 'D#3', 'E3', 'F3', 'F#3', 'G3', 'G#3', 'A3', 'A#3', 'B3', 'C4', 'C#4', 'D4', 'D#4', 'E4'],
      'E': ['E2', 'F2', 'F#2', 'G2', 'G#2', 'A2', 'A#2', 'B2', 'C3', 'C#3', 'D3', 'D#3', 'E3', 'F3', 'F#3', 'G3', 'G#3', 'A3', 'A#3', 'B3']
    };
    
    // Guitar preset configurations
    this.guitarPresets = {
      'acoustic': {
        attackTime: 0.005,
        decayTime: 0.1,
        sustainLevel: 0.4,
        releaseTime: 0.3,
        harmonicMix: [
          { type: 'triangle', level: 0.7, detune: 0 },       // Fundamental
          { type: 'triangle', level: 0.2, detune: 1200 },    // Octave
          { type: 'sine', level: 0.1, detune: -1200 },       // Sub-harmonic
          { type: 'sine', level: 0.05, detune: 1902 },       // Fifth above octave
          { type: 'sine', level: 0.03, detune: 2400 }        // Second octave
        ],
        filterFreq: 3,
        resonance: 1,
        reverbLevel: 0.2
      },
      'electric': {
        attackTime: 0.001,
        decayTime: 0.2,
        sustainLevel: 0.5,
        releaseTime: 0.1,
        harmonicMix: [
          { type: 'sawtooth', level: 0.6, detune: 0 },       // Fundamental
          { type: 'square', level: 0.15, detune: 1200 },     // Octave
          { type: 'sawtooth', level: 0.1, detune: -1200 },   // Sub-harmonic
          { type: 'sine', level: 0.05, detune: 1902 },       // Fifth above octave
          { type: 'sawtooth', level: 0.05, detune: 700 }     // Dissonant overtone for character
        ],
        filterFreq: 4,
        resonance: 3,
        reverbLevel: 0.1
      },
      'classical': {
        attackTime: 0.01,
        decayTime: 0.1,
        sustainLevel: 0.6,
        releaseTime: 0.4,
        harmonicMix: [
          { type: 'sine', level: 0.7, detune: 0 },          // Fundamental
          { type: 'sine', level: 0.2, detune: 1200 },       // Octave
          { type: 'triangle', level: 0.05, detune: -1200 }, // Sub-harmonic
          { type: 'sine', level: 0.03, detune: 1902 },      // Fifth above octave
          { type: 'sine', level: 0.02, detune: 2400 }       // Second octave
        ],
        filterFreq: 2.5,
        resonance: 0.5,
        reverbLevel: 0.3
      }
    };
  }
  
  /**
   * Initialize the audio context and effects chain
   * @returns {boolean} - Success state
   */
  initialize() {
    if (this.initialized) return true;
    
    try {
      // Create audio context
      const AudioContext = window.AudioContext || window.webkitAudioContext;
      this.context = new AudioContext();
      
      // Create master gain node
      this.masterGain = this.context.createGain();
      this.masterGain.gain.value = this.volume;
      
      // Create compressor for dynamics processing
      this.compressor = this.context.createDynamicsCompressor();
      this.compressor.threshold.value = -24;
      this.compressor.knee.value = 30;
      this.compressor.ratio.value = 12;
      this.compressor.attack.value = 0.003;
      this.compressor.release.value = 0.25;
      
      // Create reverb effect
      this.initializeReverb();
      
      // Connect the audio graph
      this.compressor.connect(this.masterGain);
      this.masterGain.connect(this.context.destination);
      
      this.initialized = true;
      return true;
    } catch (error) {
      console.error('Failed to initialize audio engine:', error);
      return false;
    }
  }
  
  /**
   * Create and initialize the reverb effect
   */
  initializeReverb() {
    // Create convolver node for reverb
    this.reverbNode = this.context.createConvolver();
    
    // Create impulse response for reverb
    const duration = 2.5;
    const decay = 2.0;
    const sampleRate = this.context.sampleRate;
    const length = sampleRate * duration;
    const impulse = this.context.createBuffer(2, length, sampleRate);
    
    // Generate reverb impulse response
    for (let channel = 0; channel < 2; channel++) {
      const channelData = impulse.getChannelData(channel);
      for (let i = 0; i < length; i++) {
        const n = length - i;
        channelData[i] = (Math.random() * 2 - 1) * Math.pow(n / length, decay);
      }
    }
    
    // Set the impulse response and connect to the audio graph
    this.reverbNode.buffer = impulse;
    this.reverbNode.connect(this.compressor);
  }
  
  /**
   * Resume audio context if suspended (for browsers that require user interaction)
   */
  async resumeContext() {
    if (this.context && this.context.state === 'suspended') {
      try {
        await this.context.resume();
        return true;
      } catch (error) {
        console.error('Failed to resume audio context:', error);
        return false;
      }
    }
    return true;
  }
  
  /**
   * Set the master volume
   * @param {number} value - Volume level (0.0 to 1.0)
   */
  setVolume(value) {
    this.volume = Math.max(0, Math.min(1, value));
    if (this.masterGain) {
      this.masterGain.gain.value = this.volume;
    }
  }
  
  /**
   * Set the guitar type preset
   * @param {string} type - Guitar type (acoustic, electric, classical)
   */
  setGuitarType(type) {
    if (this.guitarPresets[type]) {
      this.guitarType = type;
    }
  }
  
  /**
   * Play a musical note with the current guitar settings
   * @param {string} note - Note name (e.g. 'E4')
   * @param {number} duration - Note duration in seconds
   * @param {Function} callback - Optional callback when note finishes
   */
  playNote(note, duration, callback = null) {
    if (!this.initialized) {
      if (!this.initialize()) return;
    }
    
    // Get frequency for the note
    const frequency = this.noteFrequencies[note];
    if (!frequency) {
      console.error(`Invalid note: ${note}`);
      return;
    }
    
    // Get current guitar preset
    const preset = this.guitarPresets[this.guitarType];
    const now = this.context.currentTime;
    
    // Create filter for tone shaping
    const filter = this.context.createBiquadFilter();
    filter.type = 'lowpass';
    filter.frequency.setValueAtTime(frequency * preset.filterFreq, now);
    filter.Q.setValueAtTime(preset.resonance, now);
    
    // Create oscillators for each harmonic
    const oscillators = [];
    const gains = [];
    
    preset.harmonicMix.forEach(harmonic => {
      // Create oscillator
      const osc = this.context.createOscillator();
      osc.type = harmonic.type;
      osc.frequency.setValueAtTime(frequency, now);
      osc.detune.setValueAtTime(harmonic.detune, now);
      
      // Create gain node for this oscillator
      const gain = this.context.createGain();
      gain.gain.setValueAtTime(0, now);
      
      // Connect oscillator to its gain node
      osc.connect(gain);
      gain.connect(filter);
      
      // Store for later use
      oscillators.push(osc);
      gains.push(gain);
      
      // Apply ADSR envelope
      gain.gain.linearRampToValueAtTime(harmonic.level, now + preset.attackTime);
      gain.gain.exponentialRampToValueAtTime(harmonic.level * preset.sustainLevel, now + preset.attackTime + preset.decayTime);
      gain.gain.exponentialRampToValueAtTime(0.001, now + duration); // Prevent zero value error
    });
    
    // Connect filter to main effects chain
    filter.connect(this.compressor);
    
    // Add reverb send
    if (this.reverbNode) {
      const reverbSend = this.context.createGain();
      reverbSend.gain.setValueAtTime(preset.reverbLevel, now);
      filter.connect(reverbSend);
      reverbSend.connect(this.reverbNode);
    }
    
    // Subtle vibrato for realism
    const lfo = this.context.createOscillator();
    const lfoGain = this.context.createGain();
    lfo.frequency.setValueAtTime(5, now);
    lfoGain.gain.setValueAtTime(3, now);
    lfo.connect(lfoGain);
    lfoGain.connect(oscillators[0].frequency);
    
    // Start all oscillators
    oscillators.forEach(osc => osc.start(now));
    lfo.start(now);
    
    // Stop oscillators after duration
    oscillators.forEach(osc => osc.stop(now + duration));
    lfo.stop(now + duration);
    
    // Cleanup and callback
    if (callback) {
      setTimeout(() => callback(), duration * 1000);
    }
  }
  
  /**
   * Play a chord (multiple notes simultaneously)
   * @param {string[]} notes - Array of note names
   * @param {number} duration - Note duration in seconds
   * @param {number} strum - Strum timing in milliseconds
   */
  playChord(notes, duration, strum = 25) {
    if (!this.initialized) {
      if (!this.initialize()) return;
    }
    
    // Play each note with a slight delay for realistic strumming
    notes.forEach((note, index) => {
      setTimeout(() => {
        this.playNote(note, duration);
      }, index * strum);
    });
  }
  
  /**
   * Convert a guitar string and fret position to a note name
   * @param {string} string - Guitar string name (e, B, G, D, A, E)
   * @param {number} fret - Fret number
   * @returns {string} - Note name
   */
  getNoteFromStringAndFret(string, fret) {
    if (this.stringNotes[string] && fret < this.stringNotes[string].length) {
      return this.stringNotes[string][fret];
    }
    return null;
  }
}

// Create a global instance of the audio engine
const audioEngine = new AudioEngine();