/* Component styles for Guitar Tab Player */

/* Card component */
.card {
  background-color: var(--card-bg);
  border-radius: var(--border-radius-md);
  box-shadow: var(--card-shadow);
  padding: var(--spacing-lg);
  transition: background-color var(--transition-normal), box-shadow var(--transition-normal);
}

/* Buttons */
.button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  background-color: var(--accent);
  color: var(--button-text);
  border: none;
  border-radius: var(--border-radius-md);
  padding: var(--spacing-sm) var(--spacing-md);
  font-family: var(--font-family-main);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color var(--transition-fast), transform var(--transition-fast);
}

.button:hover {
  background-color: var(--accent-hover);
  transform: translateY(-2px);
}

.button:active {
  transform: translateY(0);
}

.button:disabled {
  background-color: var(--text-secondary);
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.button svg {
  width: 16px;
  height: 16px;
}

/* Icon button */
.icon-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  color: var(--text-primary);
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  cursor: pointer;
  transition: background-color var(--transition-fast), color var(--transition-fast);
}

.icon-button:hover {
  background-color: rgba(108, 92, 231, 0.1);
  color: var(--accent);
}

/* Circle buttons */
.circle-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 56px;
  height: 56px;
  border: none;
  border-radius: 50%;
  background-color: var(--accent);
  color: var(--button-text);
  cursor: pointer;
  transition: background-color var(--transition-fast), transform var(--transition-fast);
}

.circle-button:hover {
  background-color: var(--accent-hover);
  transform: scale(1.05);
}

.circle-button:active {
  transform: scale(0.98);
}

.circle-button:disabled {
  background-color: var(--text-secondary);
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.play-button svg, .stop-button svg {
  width: 24px;
  height: 24px;
}

/* Tab input container */
.tab-input-container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.tab-actions {
  display: flex;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

.tab-textarea-container {
  position: relative;
  border-radius: var(--border-radius-md);
  background-color: var(--tab-bg);
  transition: background-color var(--transition-normal);
}

.tab-textarea {
  width: 100%;
  height: 200px;
  padding: var(--spacing-md);
  background-color: transparent;
  color: var(--text-primary);
  border: 1px solid var(--border);
  border-radius: var(--border-radius-md);
  font-family: var(--font-family-mono);
  font-size: 0.875rem;
  line-height: 1.5;
  resize: vertical;
  transition: border-color var(--transition-fast);
}

.tab-textarea:focus {
  outline: none;
  border-color: var(--accent);
}

/* Playback controls */
.playback-controls {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.main-controls {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
  margin-bottom: var(--spacing-sm);
}

.progress-container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  align-items: center;
}

.progress-bar {
  width: 100%;
  height: 6px;
  background-color: var(--progress-bg);
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  width: 0;
  background-color: var(--progress-fill);
  transition: width 0.3s ease;
}

.status-text {
  font-size: 0.875rem;
  color: var(--text-secondary);
  text-align: center;
}

.status-text.playing {
  color: #2ecc71;
}

.status-text.stopped {
  color: #e74c3c;
}

/* Sound controls */
.sound-controls {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.control-label {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.slider-container {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.slider {
  flex: 1;
  -webkit-appearance: none;
  appearance: none;
  height: 4px;
  background-color: var(--progress-bg);
  border-radius: 2px;
  outline: none;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: var(--accent);
  cursor: pointer;
  transition: transform var(--transition-fast);
}

.slider::-moz-range-thumb {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: var(--accent);
  cursor: pointer;
  transition: transform var(--transition-fast);
}

.slider::-webkit-slider-thumb:hover {
  transform: scale(1.2);
}

.slider::-moz-range-thumb:hover {
  transform: scale(1.2);
}

.slider-value {
  min-width: 40px;
  font-size: 0.875rem;
  color: var(--text-secondary);
  text-align: right;
}

.select-control {
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border);
  border-radius: var(--border-radius-sm);
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  font-family: var(--font-family-main);
  font-size: 0.875rem;
  outline: none;
  cursor: pointer;
  transition: border-color var(--transition-fast);
}

.select-control:focus {
  border-color: var(--accent);
}

/* Visualization container */
.visualization-container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.guitar-neck {
  height: 120px;
  background-color: var(--tab-bg);
  border-radius: var(--border-radius-md);
  position: relative;
  overflow: hidden;
  transition: background-color var(--transition-normal);
}

.tab-display-container {
  border-radius: var(--border-radius-md);
  background-color: var(--tab-bg);
  overflow: hidden;
  transition: background-color var(--transition-normal);
}

.tab-display {
  font-family: var(--font-family-mono);
  font-size: 0.875rem;
  line-height: 1.4;
  padding: var(--spacing-md);
  overflow-x: auto;
  color: var(--text-primary);
  white-space: pre;
  transition: color var(--transition-normal);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .card {
    padding: var(--spacing-md);
  }
  
  .circle-button {
    width: 48px;
    height: 48px;
  }
  
  .tab-textarea {
    height: 160px;
  }
  
  .guitar-neck {
    height: 100px;
  }
}

@media (max-width: 480px) {
  .tab-actions {
    justify-content: center;
  }
  
  .main-controls {
    margin-top: var(--spacing-sm);
  }
  
  .sound-controls {
    gap: var(--spacing-lg);
  }
}