export class TabParser {
  private static playbackInterval: number | null = null;
  private static onPositionChange: ((position: number) => void) | null = null;

  static parse(tabContent: string) {
    const lines = tabContent.split('\n');
    const tabLines = lines.filter(line => 
      /^[eEBbGgDdAaE]\|/.test(line.trim()) || // Standard tab format
      (/[0-9]/.test(line) && /[\-|]/.test(line)) // Alternative format
    );

    return tabLines;
  }

  static highlightPosition(content: string, position: number): string {
    if (!content) return '';

    const lines = content.split('\n');
    return lines.map(line => {
      if (!this.isTabLine(line)) return line;

      const chars = line.split('');
      const highlightIndex = this.findHighlightIndex(line, position);
      
      if (highlightIndex >= 0) {
        chars[highlightIndex] = `<span class="bg-purple-500/30 px-1 rounded">${chars[highlightIndex]}</span>`;
      }

      return chars.join('');
    }).join('\n');
  }

  private static isTabLine(line: string): boolean {
    return /^[eEBbGgDdAaE]\|/.test(line.trim()) || 
           (/[0-9]/.test(line) && /[\-|]/.test(line));
  }

  private static findHighlightIndex(line: string, position: number): number {
    let currentPos = 0;
    let inStringIdentifier = true;

    for (let i = 0; i < line.length; i++) {
      if (inStringIdentifier && line[i] === '|') {
        inStringIdentifier = false;
        continue;
      }

      if (!inStringIdentifier) {
        if (currentPos === position) return i;
        currentPos++;
      }
    }

    return -1;
  }

  static startPlayback(content: string, onPosition: (position: number) => void) {
    let position = 0;
    this.onPositionChange = onPosition;

    this.stopPlayback();

    this.playbackInterval = window.setInterval(() => {
      onPosition(position);
      position++;

      // Get the maximum position from the tab content
      const maxPosition = this.getMaxPosition(content);
      if (position >= maxPosition) {
        this.stopPlayback();
        onPosition(0);
      }
    }, 300); // Adjust timing as needed
  }

  static stopPlayback() {
    if (this.playbackInterval) {
      clearInterval(this.playbackInterval);
      this.playbackInterval = null;
    }
  }

  private static getMaxPosition(content: string): number {
    const lines = this.parse(content);
    if (lines.length === 0) return 0;

    // Find the longest line after removing string identifiers
    return Math.max(...lines.map(line => 
      line.replace(/^[a-gA-G]\|/, '').length
    ));
  }
}