/**
 * Main application script for Guitar Tab Player
 * Coordinates the UI, audio engine, and tablature parsing
 */

// Application state
const appState = {
  isPlaying: false,
  parsedNotes: [],
  currentNoteIndex: 0,
  playbackTimeout: null,
  playbackSpeed: 1.0,
  selectedGuitarType: 'acoustic'
};

// DOM Elements
const elements = {
  tabInput: document.getElementById('tabInput'),
  fileInput: document.getElementById('fileInput'),
  uploadButton: document.getElementById('uploadButton'),
  processButton: document.getElementById('processButton'),
  playButton: document.getElementById('playButton'),
  stopButton: document.getElementById('stopButton'),
  progressFill: document.getElementById('progressFill'),
  status: document.getElementById('status'),
  tabDisplay: document.getElementById('tabDisplay'),
  speedControl: document.getElementById('speedControl'),
  speedValue: document.getElementById('speedValue'),
  volumeControl: document.getElementById('volumeControl'),
  volumeValue: document.getElementById('volumeValue'),
  guitarType: document.getElementById('guitarType'),
  themeToggle: document.getElementById('themeToggle'),
  guitarVisualization: document.getElementById('guitarVisualization')
};

/**
 * Initialize the application
 */
function initializeApp() {
  // Initialize audio engine
  if (!audioEngine.initialize()) {
    Utils.showStatus('Audio engine initialization failed', 'stopped');
  }
  
  // Initialize visualization
  tabVisualization.initialize('guitarVisualization');
  
  // Set up event listeners
  setupEventListeners();
  
  // Load example tab
  loadExampleTab();
}

/**
 * Set up all event listeners
 */
function setupEventListeners() {
  // Theme toggle
  elements.themeToggle.addEventListener('click', Utils.toggleTheme);
  
  // File upload
  elements.uploadButton.addEventListener('click', () => {
    elements.fileInput.click();
  });
  
  elements.fileInput.addEventListener('change', handleFileUpload);
  
  // Process tab button
  elements.processButton.addEventListener('click', processTab);
  
  // Playback controls
  elements.playButton.addEventListener('click', playTab);
  elements.stopButton.addEventListener('click', stopPlayback);
  
  // Speed control
  elements.speedControl.addEventListener('input', e => {
    appState.playbackSpeed = parseFloat(e.target.value);
    elements.speedValue.textContent = Utils.formatNumber(appState.playbackSpeed) + 'x';
  });
  
  // Volume control
  elements.volumeControl.addEventListener('input', e => {
    const volume = parseFloat(e.target.value);
    audioEngine.setVolume(volume);
    elements.volumeValue.textContent = Math.round(volume * 100) + '%';
  });
  
  // Guitar type selection
  elements.guitarType.addEventListener('change', e => {
    appState.selectedGuitarType = e.target.value;
    audioEngine.setGuitarType(e.target.value);
  });
  
  // Window resize handler for visualization
  window.addEventListener('resize', Utils.debounce(() => {
    tabVisualization.resize();
  }, 250));
}

/**
 * Handle file uploads
 * @param {Event} event - File input change event
 */
async function handleFileUpload(event) {
  const file = event.target.files[0];
  if (!file) return;
  
  try {
    let content = '';
    
    if (file.type === 'text/plain') {
      content = await file.text();
    } else if (file.type === 'application/pdf') {
      Utils.showStatus('PDF support coming soon. Please use TXT files.', 'stopped');
      return;
    } else {
      Utils.showStatus('Unsupported file type. Please use TXT files.', 'stopped');
      return;
    }
    
    elements.tabInput.value = content;
    processTab();
  } catch (error) {
    console.error('Error reading file:', error);
    Utils.showStatus('Error reading file: ' + error.message, 'stopped');
  }
}

/**
 * Process the tablature input
 */
function processTab() {
  const tabText = elements.tabInput.value.trim();
  
  if (!tabText) {
    Utils.showStatus('Please enter or upload tablature first', 'stopped');
    return;
  }
  
  // Parse the tablature
  appState.parsedNotes = tabParser.parse(tabText);
  
  if (appState.parsedNotes.length === 0) {
    Utils.showStatus('No valid tablature found', 'stopped');
    return;
  }
  
  // Update the formatted display
  elements.tabDisplay.innerHTML = tabParser.getFormattedHTML();
  
  // Update status
  Utils.showStatus(`Tab processed: ${appState.parsedNotes.length} notes found`, 'default');
  
  // Enable play button
  elements.playButton.disabled = false;
  
  // Add visual feedback
  Utils.addTemporaryClass(elements.tabDisplay.parentNode, 'fade-in');
}

/**
 * Start playing the tablature
 */
async function playTab() {
  if (appState.parsedNotes.length === 0) {
    Utils.showStatus('Process a tablature first', 'stopped');
    return;
  }
  
  // Resume audio context if needed
  if (!(await audioEngine.resumeContext())) {
    Utils.showStatus('Could not start audio playback', 'stopped');
    return;
  }
  
  // Update UI state
  appState.isPlaying = true;
  appState.currentNoteIndex = 0;
  elements.playButton.disabled = true;
  elements.stopButton.disabled = false;
  elements.progressFill.classList.add('active');
  
  Utils.showStatus('Playing...', 'playing');
  
  // Start playback
  playNextNote();
}

/**
 * Play the next note in the sequence
 */
function playNextNote() {
  if (!appState.isPlaying || appState.currentNoteIndex >= appState.parsedNotes.length) {
    stopPlayback();
    return;
  }
  
  const currentNote = appState.parsedNotes[appState.currentNoteIndex];
  const speed = appState.playbackSpeed;
  
  // Calculate note duration based on speed
  let noteDuration = 0.5 / speed;
  
  // Adjust duration for more natural sound
  if (speed > 1.5) {
    noteDuration = 0.3 / speed; // Faster tempo = shorter notes
  } else if (speed < 0.8) {
    noteDuration = 0.7 / speed; // Slower tempo = longer notes
  }
  
  // Extract notes from the current position
  const notesToPlay = [];
  
  currentNote.notes.forEach(note => {
    const noteName = audioEngine.getNoteFromStringAndFret(note.string, note.fret);
    if (noteName) {
      notesToPlay.push(noteName);
    }
  });
  
  // Play the notes as a chord with a slight strum effect
  if (notesToPlay.length > 0) {
    audioEngine.playChord(notesToPlay, noteDuration, 15);
  }
  
  // Update visualization
  tabVisualization.visualizeNote(currentNote);
  
  // Update tab display with highlighting
  elements.tabDisplay.innerHTML = tabParser.getHighlightedHTML(currentNote.position);
  
  // Update progress bar
  const progress = (appState.currentNoteIndex / appState.parsedNotes.length) * 100;
  elements.progressFill.style.width = progress + '%';
  
  // Schedule next note
  appState.currentNoteIndex++;
  
  // Calculate timing for next note
  const nextNoteDelay = (noteDuration * 0.9) * 1000; // 90% of note duration
  appState.playbackTimeout = setTimeout(playNextNote, nextNoteDelay);
}

/**
 * Stop playback
 */
function stopPlayback() {
  appState.isPlaying = false;
  
  if (appState.playbackTimeout) {
    clearTimeout(appState.playbackTimeout);
    appState.playbackTimeout = null;
  }
  
  // Reset UI
  elements.playButton.disabled = false;
  elements.stopButton.disabled = true;
  elements.progressFill.style.width = '0%';
  elements.progressFill.classList.remove('active');
  appState.currentNoteIndex = 0;
  
  // Clear visualization
  tabVisualization.clearActiveVisualizations();
  
  // Update display without highlighting
  elements.tabDisplay.innerHTML = tabParser.getFormattedHTML();
  
  Utils.showStatus('Stopped', 'stopped');
}

/**
 * Load an example tab
 */
function loadExampleTab() {
  const exampleTab = `e|--0--2--3--2--0--|--0--2--3--2--0--|
B|--1--1--1--1--1--|--1--1--1--1--1--|
G|--0--0--0--0--0--|--0--0--0--0--0--|
D|--2--2--2--2--2--|--2--2--2--2--2--|
A|--3--3--3--3--3--|--3--3--3--3--3--|
E|-----------------|-----------------|`;
  
  elements.tabInput.value = exampleTab;
  
  // Process the example tab automatically
  processTab();
}

// Initialize when DOM is fully loaded
document.addEventListener('DOMContentLoaded', initializeApp);