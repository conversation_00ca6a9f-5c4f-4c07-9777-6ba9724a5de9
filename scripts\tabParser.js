/**
 * Tablature Parser for Guitar Tab Player
 * Parses guitar tablature text into playable note data
 */

class TabParser {
  constructor() {
    // Standard guitar tuning strings
    this.strings = ['e', 'B', 'G', 'D', 'A', 'E'];
    
    // Parsed notes data
    this.parsedNotes = [];
    
    // Raw tablature text
    this.tabText = '';
    
    // Tablature lines
    this.tabLines = [];
    
    // Regex patterns for identifying tablature lines
    this.patterns = {
      standardTab: /^[eEBbGgDdAa]\|/,
      numericTab: /[0-9]/,
      dashBar: /[\-|]/
    };
  }
  
  /**
   * Parse tablature text into playable note data
   * @param {string} text - Raw tablature text
   * @returns {Array} - Array of parsed notes
   */
  parse(text) {
    if (!text || !text.trim()) {
      return [];
    }
    
    this.tabText = text;
    this.parsedNotes = [];
    
    // Split text into lines
    const lines = text.split('\n');
    this.tabLines = [];
    
    // Filter lines to find actual tablature content
    for (let line of lines) {
      const trimmedLine = line.trim();
      
      // Check if line matches standard tab pattern (e.g., "e|---")
      if (this.patterns.standardTab.test(trimmedLine)) {
        this.tabLines.push(trimmedLine);
      }
      // Check if line contains fret numbers and dashes/bars
      else if (this.patterns.numericTab.test(trimmedLine) && 
               this.patterns.dashBar.test(trimmedLine)) {
        this.tabLines.push(trimmedLine);
      }
    }
    
    // If no valid tab lines found, return empty array
    if (this.tabLines.length === 0) {
      console.warn('No valid tablature lines found in input');
      return [];
    }
    
    // Process the tablature lines
    return this.processTabLines();
  }
  
  /**
   * Process identified tablature lines into note data
   * @returns {Array} - Array of parsed notes
   */
  processTabLines() {
    // Clean the tab lines (remove string identifiers)
    const cleanTabLines = this.tabLines.map(line => {
      return line.replace(/^[a-gA-G]\|/, '');
    });
    
    // Get the maximum line length for parsing
    const maxLength = Math.max(...cleanTabLines.map(line => line.length));
    
    // Process each position across all strings
    for (let pos = 0; pos < maxLength; pos++) {
      const notesAtPosition = [];
      let hasNote = false;
      
      // Check each string for notes at this position
      for (let stringIndex = 0; stringIndex < Math.min(cleanTabLines.length, 6); stringIndex++) {
        const line = cleanTabLines[stringIndex];
        
        if (pos < line.length) {
          const char = line[pos];
          
          // Check if it's a fret number
          if (/\d/.test(char)) {
            const fret = parseInt(char, 10);
            const stringName = this.strings[stringIndex];
            
            // Add the note to our collection
            notesAtPosition.push({
              string: stringName,
              fret: fret,
              position: pos
            });
            
            hasNote = true;
          }
        }
      }
      
      // If we found notes at this position, add them to our parsed data
      if (hasNote) {
        this.parsedNotes.push({
          notes: notesAtPosition,
          position: pos,
          // Default timing - will be adjusted by playback speed
          timing: pos * 0.3
        });
      }
    }
    
    return this.parsedNotes;
  }
  
  /**
   * Convert the raw tablature to HTML with highlighting support
   * @returns {string} - HTML formatted tablature
   */
  getFormattedHTML() {
    // Replace spaces with non-breaking spaces to preserve formatting
    return this.tabText.replace(/ /g, '&nbsp;');
  }
  
  /**
   * Highlight a specific note position in the tablature
   * @param {number} position - Position to highlight
   * @returns {string} - HTML with the current note highlighted
   */
  getHighlightedHTML(position) {
    if (!this.tabText) return '';
    
    // Split the tab into lines
    const lines = this.tabText.split('\n');
    const highlightedLines = [];
    
    // Process each line
    lines.forEach(line => {
      let processedLine = '';
      let currentPos = 0;
      let inStringIdentifier = true;
      
      // Process each character
      for (let i = 0; i < line.length; i++) {
        const char = line[i];
        
        // Handle string identifiers (e.g., "e|")
        if (inStringIdentifier && char === '|') {
          inStringIdentifier = false;
          processedLine += char;
          continue;
        }
        
        if (inStringIdentifier) {
          processedLine += char;
          continue;
        }
        
        // Check if this is the position to highlight
        if (currentPos === position && /\d/.test(char)) {
          processedLine += `<span class="current-note">${char}</span>`;
        } else {
          processedLine += char;
        }
        
        // Increment position counter for actual tablature content
        if (!inStringIdentifier) {
          currentPos++;
        }
      }
      
      highlightedLines.push(processedLine);
    });
    
    // Join lines with line breaks for HTML display
    return highlightedLines.join('<br>');
  }
  
  /**
   * Get total duration of the tablature in seconds
   * @param {number} speed - Playback speed multiplier
   * @returns {number} - Duration in seconds
   */
  getTotalDuration(speed = 1.0) {
    if (this.parsedNotes.length === 0) return 0;
    
    // Get the last note's timing
    const lastNote = this.parsedNotes[this.parsedNotes.length - 1];
    // Add some padding for the last note to play fully
    return (lastNote.timing / speed) + 1.0;
  }
  
  /**
   * Get the number of notes parsed
   * @returns {number} - Note count
   */
  getNoteCount() {
    return this.parsedNotes.length;
  }
}

// Create a global instance of the tab parser
const tabParser = new TabParser();