import * as pdfjsLib from 'pdfjs-dist';

export class PDFParser {
  private static initialized = false;

  static async initialize() {
    if (this.initialized) return;
    pdfjsLib.GlobalWorkerOptions.workerSrc = `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjsLib.version}/pdf.worker.min.js`;
    this.initialized = true;
  }

  static async extractTabFromPDF(file: File): Promise<string> {
    await this.initialize();

    // Read file as ArrayBuffer
    const arrayBuffer = await file.arrayBuffer();
    
    // Load PDF document
    const pdf = await pdfjsLib.getDocument({ data: arrayBuffer }).promise;
    
    let tabContent = '';
    
    // Process each page
    for (let i = 1; i <= pdf.numPages; i++) {
      const page = await pdf.getPage(i);
      const textContent = await page.getTextContent();
      
      // Extract text content and analyze for tab patterns
      const pageText = textContent.items
        .map(item => ('str' in item) ? item.str : '')
        .join('\n');
      
      // Look for tab patterns (|----|)
      const tabLines = pageText.split('\n').filter(line => {
        return line.includes('|') && /[-0-9h~\\\/pb\(\)]{2,}/.test(line);
      });
      
      if (tabLines.length > 0) {
        // Group tab lines together
        let currentTab = '';
        for (const line of tabLines) {
          if (line.trim().length > 0) {
            currentTab += line + '\n';
          } else if (currentTab.length > 0) {
            tabContent += currentTab + '\n\n';
            currentTab = '';
          }
        }
        if (currentTab.length > 0) {
          tabContent += currentTab + '\n\n';
        }
      }
    }

    // Clean up and normalize tab content
    tabContent = this.normalizeTabContent(tabContent);
    
    return tabContent;
  }

  private static normalizeTabContent(content: string): string {
    return content
      // Remove extra whitespace
      .replace(/\s+\|/g, '|')
      .replace(/\|\s+/g, '|')
      // Ensure consistent spacing between notes
      .replace(/(-+)/g, '-')
      .replace(/(\|[0-9]+)/g, '|$1')
      // Remove empty lines
      .split('\n')
      .filter(line => line.trim().length > 0)
      .join('\n');
  }

  static async analyzeTabStructure(content: string) {
    // Identify string tuning
    const stringLines = content.split('\n').filter(line => line.includes('|'));
    const stringCount = stringLines.length;
    
    // Detect time signature and tempo if present
    const timeSignature = this.detectTimeSignature(content);
    const tempo = this.detectTempo(content);
    
    return {
      stringCount,
      timeSignature,
      tempo,
      hasChords: this.detectChords(content),
      hasTablature: stringCount >= 6,
      measures: this.countMeasures(content)
    };
  }

  private static detectTimeSignature(content: string): string {
    const timeSignatureMatch = content.match(/(\d+\/\d+)/);
    return timeSignatureMatch ? timeSignatureMatch[1] : '4/4';
  }

  private static detectTempo(content: string): number | null {
    const tempoMatch = content.match(/(?:tempo|bpm)[=\s]+(\d+)/i);
    return tempoMatch ? parseInt(tempoMatch[1]) : null;
  }

  private static detectChords(content: string): boolean {
    const chordPattern = /[A-G][#b]?(maj|min|m|M|aug|dim|7|9|13)/;
    return chordPattern.test(content);
  }

  private static countMeasures(content: string): number {
    const lines = content.split('\n');
    let measureCount = 0;
    
    for (const line of lines) {
      if (line.includes('|')) {
        measureCount = Math.max(
          measureCount,
          (line.match(/\|/g) || []).length - 1
        );
      }
    }
    
    return measureCount;
  }
}