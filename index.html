<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Guitar Tab Player</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/components.css">
    <link rel="stylesheet" href="styles/animations.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&family=JetBrains+Mono:wght@400;600&display=swap" rel="stylesheet">
</head>
<body class="theme-dark">
    <div class="app-container">
        <header class="app-header">
            <div class="logo-container">
                <div class="logo-icon">
                    <span class="guitar-string"></span>
                    <span class="guitar-string"></span>
                    <span class="guitar-string"></span>
                    <span class="guitar-string"></span>
                    <span class="guitar-string"></span>
                    <span class="guitar-string"></span>
                </div>
                <h1>Guitar Tab Player</h1>
            </div>
            <div class="header-controls">
                <button id="themeToggle" class="icon-button" aria-label="Toggle theme">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="5"></circle><line x1="12" y1="1" x2="12" y2="3"></line><line x1="12" y1="21" x2="12" y2="23"></line><line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line><line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line><line x1="1" y1="12" x2="3" y2="12"></line><line x1="21" y1="12" x2="23" y2="12"></line><line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line><line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line></svg>
                </button>
            </div>
        </header>

        <main class="main-content">
            <section class="tab-section">
                <div class="section-header">
                    <h2>Your Tablature</h2>
                </div>
                
                <div class="tab-input-container card">
                    <div class="tab-actions">
                        <button id="uploadButton" class="button">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path><polyline points="17 8 12 3 7 8"></polyline><line x1="12" y1="3" x2="12" y2="15"></line></svg>
                            Upload Tab
                            <span class="file-formats">(TXT, PDF)</span>
                        </button>
                        <input type="file" id="fileInput" accept=".txt,.pdf" hidden>
                        
                        <button id="processButton" class="button">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 11A8.1 8.1 0 0 0 4.5 9"></path><path d="M4 5v4h4"></path><path d="M4 13a8.1 8.1 0 0 0 15.5 2"></path><path d="M20 19v-4h-4"></path></svg>
                            Process Tab
                        </button>
                    </div>
                    
                    <div class="tab-textarea-container">
                        <textarea id="tabInput" class="tab-textarea" placeholder="Paste your guitar tablature here or drag & drop a file...

Example:
e|--0--2--3--2--0--|
B|--1--1--1--1--1--|
G|--0--0--0--0--0--|
D|--2--2--2--2--2--|
A|--3--3--3--3--3--|
E|-----------------|

Supported formats: TXT, PDF"></textarea>
                    </div>
                </div>
            </section>

            <section class="controls-section">
                <div class="playback-controls card">
                    <div class="main-controls">
                        <button id="playButton" class="circle-button play-button" disabled>
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polygon points="5 3 19 12 5 21 5 3"></polygon></svg>
                        </button>
                        <button id="stopButton" class="circle-button stop-button" disabled>
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect></svg>
                        </button>
                    </div>
                    
                    <div class="progress-container">
                        <div class="progress-bar">
                            <div class="progress-fill" id="progressFill"></div>
                        </div>
                        <p id="status" class="status-text">Ready to play</p>
                    </div>
                    
                    <div class="sound-controls">
                        <div class="control-group">
                            <label for="speedControl" class="control-label">Speed</label>
                            <div class="slider-container">
                                <input type="range" id="speedControl" min="0.5" max="3" step="0.1" value="1" class="slider">
                                <span id="speedValue" class="slider-value">1.0x</span>
                            </div>
                        </div>
                        
                        <div class="control-group">
                            <label for="volumeControl" class="control-label">Volume</label>
                            <div class="slider-container">
                                <input type="range" id="volumeControl" min="0" max="1" step="0.1" value="0.5" class="slider">
                                <span id="volumeValue" class="slider-value">50%</span>
                            </div>
                        </div>
                        
                        <div class="control-group">
                            <label for="guitarType" class="control-label">Guitar Type</label>
                            <select id="guitarType" class="select-control">
                                <option value="acoustic">Acoustic</option>
                                <option value="electric">Electric</option>
                                <option value="classical">Classical</option>
                            </select>
                        </div>
                    </div>
                </div>
            </section>

            <section class="visualization-section">
                <div class="section-header">
                    <h2>Tablature Visualization</h2>
                </div>
                
                <div class="visualization-container card">
                    <div class="guitar-neck" id="guitarVisualization">
                        <!-- Visualization will be rendered here -->
                    </div>
                    
                    <div class="tab-display-container">
                        <pre id="tabDisplay" class="tab-display">No tablature loaded yet...</pre>
                    </div>
                </div>
            </section>
        </main>

        <footer class="app-footer">
            <p>Guitar Tab Player © 2025 | Created with Web Audio API</p>
        </footer>
    </div>

    <script src="scripts/utils.js"></script>
    <script src="scripts/audioEngine.js"></script>
    <script src="scripts/tabParser.js"></script>
    <script src="scripts/pdfParser.js"></script>
    <script src="scripts/visualization.js"></script>
    <script src="scripts/main.js"></script>
</body>
</html>